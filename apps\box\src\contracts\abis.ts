// Contract ABIs for PoolStorage and RewardEngine contracts

export const POOL_STORAGE_ABI = [
  // Pool management functions
  {
    "inputs": [
      {"internalType": "string", "name": "name", "type": "string"},
      {"internalType": "string", "name": "region", "type": "string"},
      {"internalType": "string", "name": "parent", "type": "string"}
    ],
    "name": "createPool",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}],
    "name": "joinPool",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}],
    "name": "leavePool",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}],
    "name": "cancelJoinRequest",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"internalType": "address", "name": "account", "type": "address"},
      {"internalType": "bool", "name": "vote", "type": "bool"}
    ],
    "name": "voteJoinRequest",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  // Pool query functions
  {
    "inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}],
    "name": "getPool",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "poolId", "type": "uint256"},
          {"internalType": "string", "name": "name", "type": "string"},
          {"internalType": "string", "name": "region", "type": "string"},
          {"internalType": "string", "name": "parent", "type": "string"},
          {"internalType": "address[]", "name": "participants", "type": "address[]"},
          {"internalType": "uint256", "name": "replicationFactor", "type": "uint256"}
        ],
        "internalType": "struct PoolStorage.Pool",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "offset", "type": "uint256"},
      {"internalType": "uint256", "name": "limit", "type": "uint256"}
    ],
    "name": "listPools",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "poolId", "type": "uint256"},
          {"internalType": "string", "name": "name", "type": "string"},
          {"internalType": "string", "name": "region", "type": "string"},
          {"internalType": "string", "name": "parent", "type": "string"},
          {"internalType": "address[]", "name": "participants", "type": "address[]"},
          {"internalType": "uint256", "name": "replicationFactor", "type": "uint256"}
        ],
        "internalType": "struct PoolStorage.Pool[]",
        "name": "",
        "type": "tuple[]"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
    "name": "getUserPool",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "poolId", "type": "uint256"},
          {"internalType": "uint256", "name": "requestPoolId", "type": "uint256"},
          {"internalType": "address", "name": "account", "type": "address"}
        ],
        "internalType": "struct PoolStorage.UserPool",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"internalType": "address", "name": "account", "type": "address"}
    ],
    "name": "getJoinRequest",
    "outputs": [
      {
        "components": [
          {"internalType": "address", "name": "account", "type": "address"},
          {"internalType": "uint256", "name": "poolId", "type": "uint256"},
          {"internalType": "address[]", "name": "voted", "type": "address[]"},
          {"internalType": "uint256", "name": "positive_votes", "type": "uint256"},
          {"internalType": "uint256", "name": "negative_votes", "type": "uint256"}
        ],
        "internalType": "struct PoolStorage.JoinRequest",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  
  // Events
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "creator", "type": "address"},
      {"indexed": false, "internalType": "string", "name": "name", "type": "string"}
    ],
    "name": "PoolCreated",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "account", "type": "address"}
    ],
    "name": "JoinRequested",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "account", "type": "address"},
      {"indexed": true, "internalType": "address", "name": "voter", "type": "address"},
      {"indexed": false, "internalType": "bool", "name": "vote", "type": "bool"}
    ],
    "name": "JoinRequestVoted",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "account", "type": "address"}
    ],
    "name": "UserJoined",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "account", "type": "address"}
    ],
    "name": "UserLeft",
    "type": "event"
  }
] as const;

export const REWARD_ENGINE_ABI = [
  // Reward management functions
  {
    "inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}],
    "name": "claimRewards",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"internalType": "address[]", "name": "accounts", "type": "address[]"},
      {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}
    ],
    "name": "distributeRewards",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  // Reward query functions
  {
    "inputs": [
      {"internalType": "address", "name": "account", "type": "address"},
      {"internalType": "uint256", "name": "poolId", "type": "uint256"}
    ],
    "name": "getRewards",
    "outputs": [
      {
        "components": [
          {"internalType": "address", "name": "account", "type": "address"},
          {"internalType": "uint256", "name": "poolId", "type": "uint256"},
          {"internalType": "uint256", "name": "amount", "type": "uint256"},
          {"internalType": "uint256", "name": "lastClaimEpoch", "type": "uint256"}
        ],
        "internalType": "struct RewardEngine.Reward",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
    "name": "getTotalRewards",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "account", "type": "address"},
      {"internalType": "uint256", "name": "poolId", "type": "uint256"}
    ],
    "name": "getClaimableRewards",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  
  // Events
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "address", "name": "account", "type": "address"},
      {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "RewardsClaimed",
    "type": "event"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "uint256", "name": "poolId", "type": "uint256"},
      {"indexed": false, "internalType": "address[]", "name": "accounts", "type": "address[]"},
      {"indexed": false, "internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}
    ],
    "name": "RewardsDistributed",
    "type": "event"
  }
] as const;
