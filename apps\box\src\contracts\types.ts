// Contract types and interfaces for PoolStorage and RewardEngine

export interface PoolInfo {
  poolId: string;
  name: string;
  region: string;
  parent: string;
  participants: string[];
  replicationFactor: number;
}

export interface UserPoolInfo {
  poolId: string;
  requestPoolId: string;
  account: string;
}

export interface JoinRequest {
  account: string;
  poolId: string;
  voted: string[];
  positive_votes: number;
  negative_votes: number;
}

export interface RewardInfo {
  account: string;
  poolId: string;
  amount: string;
  lastClaimEpoch: number;
}

export interface ContractAddresses {
  poolStorage: string;
  rewardEngine: string;
}

export interface ChainConfig {
  chainId: string;
  name: string;
  rpcUrl: string;
  blockExplorer: string;
  contracts: ContractAddresses;
  requiresAuth?: boolean;
}

// Contract method signatures
export interface PoolStorageContract {
  // Pool management
  createPool(name: string, region: string, parent: string): Promise<string>;
  joinPool(poolId: string): Promise<void>;
  leavePool(poolId: string): Promise<void>;
  cancelJoinRequest(poolId: string): Promise<void>;
  voteJoinRequest(poolId: string, account: string, vote: boolean): Promise<void>;
  
  // Pool queries
  getPool(poolId: string): Promise<PoolInfo>;
  listPools(offset: number, limit: number): Promise<PoolInfo[]>;
  getUserPool(account: string): Promise<UserPoolInfo>;
  getJoinRequest(poolId: string, account: string): Promise<JoinRequest>;
  
  // Events
  PoolCreated: string;
  JoinRequested: string;
  JoinRequestVoted: string;
  UserJoined: string;
  UserLeft: string;
}

export interface RewardEngineContract {
  // Reward management
  claimRewards(poolId: string): Promise<void>;
  distributeRewards(poolId: string, accounts: string[], amounts: string[]): Promise<void>;
  
  // Reward queries
  getRewards(account: string, poolId: string): Promise<RewardInfo>;
  getTotalRewards(account: string): Promise<string>;
  getClaimableRewards(account: string, poolId: string): Promise<string>;
  
  // Events
  RewardsClaimed: string;
  RewardsDistributed: string;
}

export type SupportedChain = 'base' | 'skale';

export interface ContractError extends Error {
  code?: string;
  reason?: string;
  transaction?: any;
}
