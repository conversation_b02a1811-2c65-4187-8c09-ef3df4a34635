import { ChainConfig, SupportedChain } from './types';
import { baseChainId, skaleChainId } from '../utils/walletConnectConifg';

// Contract addresses for each supported chain
export const CONTRACT_ADDRESSES: Record<SupportedChain, ChainConfig> = {
  base: {
    chainId: baseChainId,
    name: 'Base',
    rpcUrl: 'https://mainnet.base.org',
    blockExplorer: 'https://basescan.org',
    requiresAuth: true,
    contracts: {
      // TODO: Replace with actual deployed contract addresses
      poolStorage: '******************************************',
      rewardEngine: '******************************************',
    },
  },
  skale: {
    chainId: skaleChainId,
    name: 'SKALE Europa Hub',
    rpcUrl: 'https://mainnet.skalenodes.com/v1/elated-tan-skat',
    blockExplorer: 'https://elated-tan-skat.explorer.mainnet.skalenodes.com',
    requiresAuth: false,
    contracts: {
      // TODO: Replace with actual deployed contract addresses
      poolStorage: '******************************************',
      rewardEngine: '******************************************',
    },
  },
};

// Default chain for pool operations
export const DEFAULT_CHAIN: SupportedChain = 'skale';

// Authorization code for Base chain access
export const BASE_AUTH_CODE = '9870';

// Helper function to get contract config by chain ID
export const getChainConfig = (chainId: string): ChainConfig | null => {
  const chain = Object.values(CONTRACT_ADDRESSES).find(
    config => config.chainId === chainId
  );
  return chain || null;
};

// Helper function to get contract config by chain name
export const getChainConfigByName = (chainName: SupportedChain): ChainConfig => {
  return CONTRACT_ADDRESSES[chainName];
};

// Validate if a chain is supported for pool operations
export const isSupportedChain = (chainId: string): boolean => {
  return Object.values(CONTRACT_ADDRESSES).some(
    config => config.chainId === chainId
  );
};

// Get all supported chain IDs
export const getSupportedChainIds = (): string[] => {
  return Object.values(CONTRACT_ADDRESSES).map(config => config.chainId);
};

// Chain display names for UI
export const CHAIN_DISPLAY_NAMES: Record<SupportedChain, string> = {
  base: 'Base Network',
  skale: 'SKALE Europa Hub',
};

// RPC endpoints with fallbacks
export const RPC_ENDPOINTS: Record<SupportedChain, string[]> = {
  base: [
    'https://mainnet.base.org',
    'https://base-mainnet.public.blastapi.io',
    'https://base.gateway.tenderly.co',
  ],
  skale: [
    'https://mainnet.skalenodes.com/v1/elated-tan-skat',
    // Add more SKALE RPC endpoints if available
  ],
};

// Block explorer URLs for transactions
export const BLOCK_EXPLORERS: Record<SupportedChain, string> = {
  base: 'https://basescan.org',
  skale: 'https://elated-tan-skat.explorer.mainnet.skalenodes.com',
};

// Gas settings for each chain
export const GAS_SETTINGS: Record<SupportedChain, {
  gasLimit: number;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
}> = {
  base: {
    gasLimit: 500000,
    maxFeePerGas: '1000000000', // 1 gwei
    maxPriorityFeePerGas: '1000000000', // 1 gwei
  },
  skale: {
    gasLimit: 500000,
    // SKALE has zero gas fees
  },
};

// Contract method gas limits
export const METHOD_GAS_LIMITS = {
  joinPool: 200000,
  leavePool: 150000,
  cancelJoinRequest: 100000,
  voteJoinRequest: 120000,
  claimRewards: 180000,
  createPool: 250000,
} as const;

export type ContractMethod = keyof typeof METHOD_GAS_LIMITS;
